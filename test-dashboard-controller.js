// Quick test to verify dashboard controller structure
const dashboardController = require('./backend/src/controllers/DashboardController.js');

console.log('✅ Dashboard Controller loaded successfully');
console.log('📋 Available methods:', Object.keys(dashboardController));

// Check if the main method exists
if (dashboardController.getDashboardSummary) {
  console.log('✅ getDashboardSummary method exists');
} else {
  console.log('❌ getDashboardSummary method missing');
}

if (dashboardController.getDailyRevenueData) {
  console.log('✅ getDailyRevenueData method exists');
} else {
  console.log('❌ getDailyRevenueData method missing');
}

console.log('🎉 Dashboard Controller structure verification complete');