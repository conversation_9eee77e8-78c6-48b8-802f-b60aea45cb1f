// Test the final implementation
const dateRange = '2025-06-30';

console.log('Testing final implementation:');
console.log('Input date:', dateRange);

// Simulate the exact logic from the dashboard controller
let startDate, endDate;

if (dateRange && dateRange.match(/^\d{4}-\d{2}-\d{2}$/)) {
  // Create UTC dates that will result in exactly 06:00:00 in the SQL query
  startDate = new Date(dateRange + 'T06:00:00.000Z');
  endDate = new Date(dateRange + 'T06:00:00.000Z');
  endDate.setDate(endDate.getDate() + 1); // Next day 6:00 AM
  
  console.log(`Using specific operational date: ${dateRange} (6:00 AM to 6:00 AM next day)`);
  console.log(`SQL Format: ${startDate.toISOString().replace('T', ' ').substring(0, 19)} to ${endDate.toISOString().replace('T', ' ').substring(0, 19)}`);
}

// Simulate the parameter substitution
const queryParams = { startDate, endDate };

console.log('\n📋 Parameter Values:');
Object.keys(queryParams).forEach(key => {
  const value = queryParams[key];
  if (value instanceof Date) {
    console.log(`  @${key}: '${value.toISOString().replace('T', ' ').substring(0, 19)}'`);
  } else {
    console.log(`  @${key}: ${typeof value === 'string' ? `'${value}'` : value}`);
  }
});

// Test query substitution
const testQuery = `
SELECT * FROM tblParkwiz_Parking_Data t
WHERE t.EntryDateTime BETWEEN @startDate AND @endDate
OR t.ExitDateTime BETWEEN @startDate AND @endDate
`;

let debugQuery = testQuery;
Object.keys(queryParams).forEach(key => {
  const value = queryParams[key];
  const paramPlaceholder = `@${key}`;
  let substitutedValue;
  
  if (value instanceof Date) {
    substitutedValue = `'${value.toISOString().replace('T', ' ').substring(0, 19)}'`;
  } else if (typeof value === 'string') {
    substitutedValue = `'${value}'`;
  } else {
    substitutedValue = value;
  }
  
  debugQuery = debugQuery.replace(new RegExp(`\\${paramPlaceholder}\\b`, 'g'), substitutedValue);
});

console.log('\n🎯 EXACT QUERY WITH VALUES:');
console.log('=====================================');
console.log(debugQuery);
console.log('=====================================');

// Check if we get the expected values
const expectedStart = '2025-06-30 06:00:00';
const expectedEnd = '2025-07-01 06:00:00';
const actualStart = startDate.toISOString().replace('T', ' ').substring(0, 19);
const actualEnd = endDate.toISOString().replace('T', ' ').substring(0, 19);

console.log('\n✅ Verification:');
console.log('Start matches:', actualStart === expectedStart ? '✅' : '❌', `Got: ${actualStart}, Expected: ${expectedStart}`);
console.log('End matches:', actualEnd === expectedEnd ? '✅' : '❌', `Got: ${actualEnd}, Expected: ${expectedEnd}`);