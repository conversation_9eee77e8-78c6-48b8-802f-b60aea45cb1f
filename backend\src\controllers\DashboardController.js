const db = require('../config/database');
const sql = require('mssql');
const redisService = require('../services/RedisService');

/**
 * ===============================================================================
 * # Dashboard Controller
 * ===============================================================================
 *
 * This controller provides endpoints for dashboard metrics and visualizations
 * with role-based access control. It aggregates data from the parking system
 * to provide insights based on user roles (SuperAdmin, CompanyAdmin, PlazaManager).
 *
 * @module DashboardController
 */

/**
 * Get cache TTL based on date range
 */
function getCacheTTL(dateRange) {
  switch(dateRange) {
    case 'today':
      return 60; // 1 minute for today's data
    case 'yesterday':
      return 300; // 5 minutes for yesterday
    case 'week':
      return 600; // 10 minutes for week
    case 'month':
      return 1800; // 30 minutes for month
    case 'year':
      return 3600; // 1 hour for year
    default:
      return 300; // 5 minutes default
  }
}

/**
 * Helper function to calculate date range based on selection
 * @param {string} dateRange - The date range selection (today, yesterday, week, month, year, or specific date in YYYY-MM-DD format)
 * @returns {Object} Object with startDate and endDate
 */
function calculateDateRange(dateRange) {
  // Use current date as the reference date
  const referenceDate = new Date();
  let startDate, endDate;
  
  // Check if dateRange is a specific date in YYYY-MM-DD format
  if (dateRange && dateRange.match(/^\d{4}-\d{2}-\d{2}$/)) {
    // It's a specific date - operational day from 6:00 AM to 6:00 AM next day
    // Create UTC dates that will result in exactly 06:00:00 in the SQL query
    startDate = new Date(dateRange + 'T06:00:00.000Z');
    endDate = new Date(dateRange + 'T06:00:00.000Z');
    endDate.setDate(endDate.getDate() + 1); // Next day 6:00 AM
    
    console.log(`Using specific operational date: ${dateRange} (6:00 AM to 6:00 AM next day)`);
    console.log(`SQL Format: ${startDate.toISOString().replace('T', ' ').substring(0, 19)} to ${endDate.toISOString().replace('T', ' ').substring(0, 19)}`);
  } else {
    // It's a predefined range - operational day logic (6:00 AM to 6:00 AM)
    switch(dateRange) {
      case 'today':
        // Current operational day
        if (referenceDate.getHours() >= 6) {
          // After 6 AM - current operational day started today at 6 AM
          startDate = new Date(referenceDate);
          startDate.setHours(6, 0, 0, 0);
          endDate = new Date(referenceDate);
          endDate.setDate(endDate.getDate() + 1);
          endDate.setHours(6, 0, 0, 0);
        } else {
          // Before 6 AM - current operational day started yesterday at 6 AM
          startDate = new Date(referenceDate);
          startDate.setDate(startDate.getDate() - 1);
          startDate.setHours(6, 0, 0, 0);
          endDate = new Date(referenceDate);
          endDate.setHours(6, 0, 0, 0);
        }
        break;
      case 'yesterday':
        // Previous operational day
        if (referenceDate.getHours() >= 6) {
          // After 6 AM - yesterday's operational day
          startDate = new Date(referenceDate);
          startDate.setDate(startDate.getDate() - 1);
          startDate.setHours(6, 0, 0, 0);
          endDate = new Date(referenceDate);
          endDate.setHours(6, 0, 0, 0);
        } else {
          // Before 6 AM - day before yesterday's operational day
          startDate = new Date(referenceDate);
          startDate.setDate(startDate.getDate() - 2);
          startDate.setHours(6, 0, 0, 0);
          endDate = new Date(referenceDate);
          endDate.setDate(endDate.getDate() - 1);
          endDate.setHours(6, 0, 0, 0);
        }
        break;
      case 'week':
        // Last 7 operational days
        if (referenceDate.getHours() >= 6) {
          // After 6 AM - include current operational day
          endDate = new Date(referenceDate);
          endDate.setDate(endDate.getDate() + 1);
          endDate.setHours(6, 0, 0, 0);
        } else {
          // Before 6 AM - end at current operational day
          endDate = new Date(referenceDate);
          endDate.setHours(6, 0, 0, 0);
        }
        startDate = new Date(endDate);
        startDate.setDate(startDate.getDate() - 7);
        startDate.setHours(6, 0, 0, 0);
        break;
      case 'month':
        // Last 30 operational days
        if (referenceDate.getHours() >= 6) {
          // After 6 AM - include current operational day
          endDate = new Date(referenceDate);
          endDate.setDate(endDate.getDate() + 1);
          endDate.setHours(6, 0, 0, 0);
        } else {
          // Before 6 AM - end at current operational day
          endDate = new Date(referenceDate);
          endDate.setHours(6, 0, 0, 0);
        }
        startDate = new Date(endDate);
        startDate.setDate(startDate.getDate() - 30);
        startDate.setHours(6, 0, 0, 0);
        break;
      case 'year':
        // Last 365 operational days
        if (referenceDate.getHours() >= 6) {
          // After 6 AM - include current operational day
          endDate = new Date(referenceDate);
          endDate.setDate(endDate.getDate() + 1);
          endDate.setHours(6, 0, 0, 0);
        } else {
          // Before 6 AM - end at current operational day
          endDate = new Date(referenceDate);
          endDate.setHours(6, 0, 0, 0);
        }
        startDate = new Date(endDate);
        startDate.setDate(startDate.getDate() - 365);
        startDate.setHours(6, 0, 0, 0);
        break;
      default:
        // Default to current operational day
        if (referenceDate.getHours() >= 6) {
          // After 6 AM - current operational day started today at 6 AM
          startDate = new Date(referenceDate);
          startDate.setHours(6, 0, 0, 0);
          endDate = new Date(referenceDate);
          endDate.setDate(endDate.getDate() + 1);
          endDate.setHours(6, 0, 0, 0);
        } else {
          // Before 6 AM - current operational day started yesterday at 6 AM
          startDate = new Date(referenceDate);
          startDate.setDate(startDate.getDate() - 1);
          startDate.setHours(6, 0, 0, 0);
          endDate = new Date(referenceDate);
          endDate.setHours(6, 0, 0, 0);
        }
    }
  }
  
  // Convert to IST for logging (UTC + 5:30)
  const startIST = new Date(startDate.getTime() + (5.5 * 60 * 60 * 1000));
  const endIST = new Date(endDate.getTime() + (5.5 * 60 * 60 * 1000));
  
  console.log(`📅 Date Range (UTC): ${startDate.toISOString()} to ${endDate.toISOString()}`);
  console.log(`🇮🇳 Date Range (IST): ${startIST.toLocaleString('en-IN')} to ${endIST.toLocaleString('en-IN')}`);
  console.log(`⏰ Operational Day: ${startDate.toISOString().replace('T', ' ').substring(0, 19)} to ${endDate.toISOString().replace('T', ' ').substring(0, 19)}`);
  
  return { startDate, endDate };
}
const dashboardController = {
  /**
   * ===============================================================================
   * ## GET DASHBOARD SUMMARY
   * ===============================================================================
   *
   * Returns summary metrics for the dashboard based on user role and filters.
   * Different metrics are provided based on user role and access permissions.
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with dashboard metrics
   */
  getDashboardSummary: async (req, res) => {
    try {
      console.log('🚀 Dashboard Summary Request Started');
      const { dateRange = 'today', companyId, plazaId, laneId } = req.query;
      const { id: userId, role } = req.user;
      
      console.log('👤 User Info:', { userId, role });
      console.log('🔧 Request Filters:', { dateRange, companyId, plazaId, laneId });
      
      // Create filters object for caching
      const filters = { dateRange, companyId, plazaId, laneId };
      
      // Check Redis cache first
      const cachedData = await redisService.getDashboardSummary(userId, role, filters);
      if (cachedData) {
        console.log('✅ Dashboard summary served from Redis cache');
        return res.json({
          success: true,
          data: cachedData,
          cached: true,
          timestamp: new Date().toISOString()
        });
      }
      
      console.log('❌ Cache miss - fetching dashboard summary from database');
      
      // Calculate date range
      const { startDate, endDate } = calculateDateRange(dateRange);
      
      // Limit date range for large queries to improve performance
      let optimizedStartDate = new Date(startDate);
      let optimizedEndDate = new Date(endDate);
      
      // If date range is more than 90 days, limit to 90 days for better performance
      const maxRangeDays = 90; // 3 months max
      const requestedRangeDays = Math.floor((endDate - startDate) / (1000 * 60 * 60 * 24));
      
      if (requestedRangeDays > maxRangeDays) {
        optimizedStartDate = new Date(optimizedEndDate);
        optimizedStartDate.setDate(optimizedEndDate.getDate() - maxRangeDays);
      }
      
      // Base query parameters
      const queryParams = { 
        startDate: optimizedStartDate, 
        endDate: optimizedEndDate
      };
      
      // Apply role-based filtering
      let companyFilter = '';
      let plazaFilter = '';
      
      console.log('🔐 Applying Role-based Filtering:');
      if (role === 'CompanyAdmin') {
        companyFilter = 'AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
        console.log('  - CompanyAdmin filter applied for userId:', userId);
      } else if (role === 'PlazaManager') {
        plazaFilter = 'AND t.PlazaCode IN (SELECT p.PlazaCode FROM Plaza p JOIN UserPlaza up ON p.Id = up.PlazaId WHERE up.UserId = @userId AND up.IsActive = 1)';
        queryParams.userId = userId;
        console.log('  - PlazaManager filter applied for userId:', userId);
      } else {
        console.log('  - SuperAdmin: No role-based filtering');
      }
      
      // Entity-specific filtering
      console.log('🏢 Applying Entity-specific Filtering:');
      if (companyId) {
        companyFilter = 'AND p.CompanyId = @companyId';
        queryParams.companyId = companyId;
        console.log('  - Company filter applied for companyId:', companyId);
      }
      
      if (plazaId) {
        plazaFilter = 'AND t.PlazaCode = (SELECT PlazaCode FROM Plaza WHERE Id = @plazaId)';
        queryParams.plazaId = plazaId;
        console.log('  - Plaza filter applied for plazaId:', plazaId);
      }
      
      let laneFilter = '';
      if (laneId) {
        laneFilter = 'AND (t.EntryLane = @laneId OR t.ExitLane = @laneId)';
        queryParams.laneId = laneId;
        console.log('  - Lane filter applied for laneId:', laneId);
      }
      
      console.log('📋 Final Filters Applied:');
      console.log('  - Company Filter:', companyFilter || 'None');
      console.log('  - Plaza Filter:', plazaFilter || 'None');
      console.log('  - Lane Filter:', laneFilter || 'None');

// Optimized single query to get all parking data (entry/exit counts and revenue)
      const summaryQuery = `
        SELECT 
          -- Total Revenue (only from exits with parking fee)
          ISNULL(SUM(CASE WHEN t.ExitDateTime BETWEEN @startDate AND @endDate THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS TotalRevenue,
          
          -- Four Wheeler Data
          ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS FourWheelerRevenue,
          ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.EntryDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS FourWheelerEntryCount,
          ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS FourWheelerExitCount,
          
          -- Two Wheeler Data
          ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS TwoWheelerRevenue,
          ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.EntryDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TwoWheelerEntryCount,
          ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TwoWheelerExitCount,
          
          -- Total Counts
          ISNULL(SUM(CASE WHEN t.EntryDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TotalEntryCount,
          ISNULL(SUM(CASE WHEN t.ExitDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TotalExitCount
        FROM tblParkwiz_Parking_Data t WITH (NOLOCK)
        ${companyFilter ? 'INNER JOIN Plaza p WITH (NOLOCK) ON t.PlazaCode = p.PlazaCode' : ''}
        WHERE (t.EntryDateTime BETWEEN @startDate AND @endDate OR t.ExitDateTime BETWEEN @startDate AND @endDate)
        ${companyFilter}
        ${plazaFilter}
        ${laneFilter}
        OPTION (OPTIMIZE FOR UNKNOWN, MAXDOP 2)
      `;
      
      // Execute optimized single query with detailed logging
      console.log('🔍 Executing Optimized Summary Query Template');
      console.log('📊 Query Parameters:', queryParams);
      
      // Log parameters in readable format
      console.log('📋 Parameter Values:');
      Object.keys(queryParams).forEach(key => {
        const value = queryParams[key];
        if (value instanceof Date) {
          console.log(`  @${key}: '${value.toISOString().replace('T', ' ').substring(0, 19)}'`);
        } else {
          console.log(`  @${key}: ${typeof value === 'string' ? `'${value}'` : value}`);
        }
      });
      
      // Build the complete query by resolving template literals first
      const completeQuery = `
        SELECT 
          -- Total Revenue (only from exits with parking fee)
          ISNULL(SUM(CASE WHEN t.ExitDateTime BETWEEN @startDate AND @endDate THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS TotalRevenue,
          
          -- Four Wheeler Data
          ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS FourWheelerRevenue,
          ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.EntryDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS FourWheelerEntryCount,
          ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS FourWheelerExitCount,
          
          -- Two Wheeler Data
          ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS TwoWheelerRevenue,
          ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.EntryDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TwoWheelerEntryCount,
          ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TwoWheelerExitCount,
          
          -- Total Counts
          ISNULL(SUM(CASE WHEN t.EntryDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TotalEntryCount,
          ISNULL(SUM(CASE WHEN t.ExitDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TotalExitCount
        FROM tblParkwiz_Parking_Data t WITH (NOLOCK)
        ${companyFilter ? 'INNER JOIN Plaza p WITH (NOLOCK) ON t.PlazaCode = p.PlazaCode' : ''}
        WHERE (t.EntryDateTime BETWEEN @startDate AND @endDate OR t.ExitDateTime BETWEEN @startDate AND @endDate)
        ${companyFilter}
        ${plazaFilter}
        ${laneFilter}
        OPTION (OPTIMIZE FOR UNKNOWN, MAXDOP 2)
      `;
      
      // Create the exact query with substituted parameters for debugging
      let debugQuery = completeQuery;
      Object.keys(queryParams).forEach(key => {
        const value = queryParams[key];
        const paramPlaceholder = `@${key}`;
        let substitutedValue;
        
        if (value instanceof Date) {
          substitutedValue = `'${value.toISOString().replace('T', ' ').substring(0, 19)}'`;
        } else if (typeof value === 'string') {
          substitutedValue = `'${value}'`;
        } else {
          substitutedValue = value;
        }
        
        debugQuery = debugQuery.replace(new RegExp(`\\${paramPlaceholder}\\b`, 'g'), substitutedValue);
      });
      
      console.log('🎯 EXACT QUERY WITH VALUES:');
      console.log('=====================================');
      console.log(debugQuery);
      console.log('=====================================');
      
      const summaryResult = await db.query(summaryQuery, queryParams);
      console.log('✅ Optimized Query Result:', summaryResult.recordset[0]);
      
      // Process results with detailed logging
      const data = summaryResult.recordset[0];
      
      console.log('📈 Processing Single Query Results:');
      console.log('  Complete Data:', data);
      console.log('📊 Detailed Data Breakdown:');
      console.log('  💰 Revenue:');
      console.log('    - Total Revenue:', data.TotalRevenue || 0);
      console.log('    - Four Wheeler Revenue:', data.FourWheelerRevenue || 0);
      console.log('    - Two Wheeler Revenue:', data.TwoWheelerRevenue || 0);
      console.log('  🚗 Four Wheeler Counts:');
      console.log('    - Entry Count:', data.FourWheelerEntryCount || 0);
      console.log('    - Exit Count:', data.FourWheelerExitCount || 0);
      console.log('  🏍️ Two Wheeler Counts:');
      console.log('    - Entry Count:', data.TwoWheelerEntryCount || 0);
      console.log('    - Exit Count:', data.TwoWheelerExitCount || 0);
      console.log('  📊 Total Counts:');
      console.log('    - Total Entry Count:', data.TotalEntryCount || 0);
      console.log('    - Total Exit Count:', data.TotalExitCount || 0);
      
      // Calculate remaining vehicles (entries - exits)
      const fourWheelerRemaining = (data.FourWheelerEntryCount || 0) - (data.FourWheelerExitCount || 0);
      const twoWheelerRemaining = (data.TwoWheelerEntryCount || 0) - (data.TwoWheelerExitCount || 0);
      const totalRemaining = (data.TotalEntryCount || 0) - (data.TotalExitCount || 0);
      
      console.log('🧮 Calculated Remaining Counts:');
      console.log('  Four Wheeler:', `${data.FourWheelerEntryCount} entries - ${data.FourWheelerExitCount} exits = ${fourWheelerRemaining} remaining`);
      console.log('  Two Wheeler:', `${data.TwoWheelerEntryCount} entries - ${data.TwoWheelerExitCount} exits = ${twoWheelerRemaining} remaining`);
      console.log('  Total:', `${data.TotalEntryCount} entries - ${data.TotalExitCount} exits = ${totalRemaining} remaining`);
      
      // Data validation logging
      console.log('🔍 Data Validation:');
      if ((data.FourWheelerEntryCount || 0) === 0 && (data.TwoWheelerEntryCount || 0) === 0) {
        console.log('  ⚠️  WARNING: No entry data found for the specified date range');
      }
      if ((data.FourWheelerExitCount || 0) === 0 && (data.TwoWheelerExitCount || 0) === 0) {
        console.log('  ⚠️  WARNING: No exit data found for the specified date range');
      }
      if ((data.TotalRevenue || 0) === 0) {
        console.log('  ⚠️  WARNING: No revenue data found for the specified date range');
      }
      
      // Expected vs Actual comparison (if you have expected values)
      const expectedFourWheelerEntries = 738;
      const expectedFourWheelerExits = 562;
      if (data.FourWheelerEntryCount !== expectedFourWheelerEntries) {
        console.log(`  📊 Four Wheeler Entry Count: Expected ${expectedFourWheelerEntries}, Got ${data.FourWheelerEntryCount || 0}`);
      }
      if (data.FourWheelerExitCount !== expectedFourWheelerExits) {
        console.log(`  📊 Four Wheeler Exit Count: Expected ${expectedFourWheelerExits}, Got ${data.FourWheelerExitCount || 0}`);
      }
      
      const response = {
        // Total Revenue
        totalRevenue: data.TotalRevenue || 0,
        
        // Four Wheeler Data
        fourWheeler: {
          revenue: data.FourWheelerRevenue || 0,
          entryCount: data.FourWheelerEntryCount || 0,
          exitCount: data.FourWheelerExitCount || 0,
          remainingCount: Math.max(0, fourWheelerRemaining) // Ensure non-negative
        },
        
        // Two Wheeler Data
        twoWheeler: {
          revenue: data.TwoWheelerRevenue || 0,
          entryCount: data.TwoWheelerEntryCount || 0,
          exitCount: data.TwoWheelerExitCount || 0,
          remainingCount: Math.max(0, twoWheelerRemaining) // Ensure non-negative
        },
        
        // Total Counts
        totalCounts: {
          entryCount: data.TotalEntryCount || 0,
          exitCount: data.TotalExitCount || 0,
          remainingCount: Math.max(0, totalRemaining) // Ensure non-negative
        },
        
        // Add metadata about query optimization
        meta: {
          dateRangeOptimized: requestedRangeDays > maxRangeDays,
          originalDateRange: {
            start: startDate,
            end: endDate
          },
          queryDateRange: {
            start: optimizedStartDate,
            end: optimizedEndDate
          }
        }
      };
      
      console.log('📤 Final Dashboard Response:', JSON.stringify(response, null, 2));
      
      // Cache the results with appropriate TTL based on date range
      const cacheTTL = getCacheTTL(dateRange);
      await redisService.cacheDashboardSummary(userId, role, filters, response);
      console.log(`✅ Dashboard summary cached for ${cacheTTL} seconds`);
      
      return res.status(200).json({
        success: true,
        data: response,
        cached: false,
        message: 'Dashboard summary retrieved successfully'
      });
    } catch (error) {
      console.error('❌ Error in getDashboardSummary:', error);
      console.error('📋 Error Details:', {
        message: error.message,
        code: error.code,
        stack: error.stack,
        queryParams: queryParams || 'Not available'
      });
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve dashboard summary',
        error: error.message,
        errorCode: error.code
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET REVENUE BY PAYMENT METHOD
   * ===============================================================================
   *
   * Returns revenue breakdown by payment method with transaction counts.
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with payment method revenue data
   */
  getRevenueByPaymentMethod: async (req, res) => {
    try {
      const { dateRange = 'today', companyId, plazaId } = req.query;
      const { id: userId, role } = req.user;
      
      // Create filters object for caching
      const filters = { dateRange, companyId, plazaId };
      
      // Check Redis cache first
      const cachedData = await redisService.getRevenueByPayment(userId, role, filters);
      if (cachedData) {
        console.log('✅ Revenue by payment method served from Redis cache');
        return res.json({
          success: true,
          data: cachedData,
          cached: true,
          message: 'Revenue by payment method retrieved successfully'
        });
      }
      
      console.log('❌ Cache miss - fetching revenue by payment method from database');
      
      // Calculate date range
      const { startDate, endDate } = calculateDateRange(dateRange);
      
      // Base query parameters
      const queryParams = { 
        startDate, 
        endDate 
      };
      
      // Apply role-based filtering
      let companyFilter = '';
      let plazaFilter = '';
      
      if (role === 'CompanyAdmin') {
        companyFilter = 'AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
      } else if (role === 'PlazaManager') {
        plazaFilter = 'AND t.PlazaCode IN (SELECT p.PlazaCode FROM Plaza p JOIN UserPlaza up ON p.Id = up.PlazaId WHERE up.UserId = @userId AND up.IsActive = 1)';
        queryParams.userId = userId;
      }
      
      // Entity-specific filtering
      if (companyId) {
        companyFilter = 'AND p.CompanyId = @companyId';
        queryParams.companyId = companyId;
      }
      
      if (plazaId) {
        plazaFilter = 'AND t.PlazaCode = (SELECT PlazaCode FROM Plaza WHERE Id = @plazaId)';
        queryParams.plazaId = plazaId;
      }
      
      // Execute payment method query
      const paymentMethodQuery = `
        SELECT
          ISNULL(t.PaymentMode, 'Unknown') as paymentMode,
          ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as totalRevenue,
          COUNT(*) as transactionCount
        FROM tblParkwiz_Parking_Data t
        ${companyFilter ? 'LEFT JOIN Plaza p ON t.PlazaCode = p.PlazaCode' : ''}
        WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
        ${companyFilter}
        ${plazaFilter}
        GROUP BY t.PaymentMode
        ORDER BY totalRevenue DESC
      `;
      
      const result = await db.query(paymentMethodQuery, queryParams);
      
      // Cache the results
      const cacheTTL = getCacheTTL(dateRange);
      await redisService.cacheRevenueByPayment(userId, role, filters, result.recordset);
      console.log(`✅ Revenue by payment method cached for ${cacheTTL} seconds`);
      
      return res.status(200).json({
        success: true,
        data: result.recordset,
        cached: false,
        message: 'Revenue by payment method retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getRevenueByPaymentMethod:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve revenue by payment method',
        error: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET RECENT TRANSACTIONS
   * ===============================================================================
   *
   * Returns the most recent transactions based on user role and access permissions.
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with recent transactions
   */
  getRecentTransactions: async (req, res) => {
    try {
      const { limit = 5, companyId, plazaId } = req.query;
      const { id: userId, role } = req.user;
      
      // Base query parameters
      const queryParams = { 
        limit: parseInt(limit) 
      };
      
      // Apply role-based filtering
      let companyFilter = '';
      let plazaFilter = '';
      
      if (role === 'CompanyAdmin') {
        companyFilter = 'AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
      } else if (role === 'PlazaManager') {
        plazaFilter = 'AND t.PlazaCode IN (SELECT p.PlazaCode FROM Plaza p JOIN UserPlaza up ON p.Id = up.PlazaId WHERE up.UserId = @userId AND up.IsActive = 1)';
        queryParams.userId = userId;
      }
      
      // Entity-specific filtering
      if (companyId) {
        companyFilter = 'AND p.CompanyId = @companyId';
        queryParams.companyId = companyId;
      }
      
      if (plazaId) {
        plazaFilter = 'AND t.PlazaCode = (SELECT PlazaCode FROM Plaza WHERE Id = @plazaId)';
        queryParams.plazaId = plazaId;
      }
      
      // Execute recent transactions query
      const recentTransactionsQuery = `
        SELECT TOP(@limit)
          t.PakringDataID,
          t.PlazaName,
          t.VehicleNumber,
          t.EntryDateTime,
          t.ExitDateTime,
          t.EntryLane,
          t.ExitLane,
          t.ParkedDuration,
          t.ParkingFee,
          t.iTotalGSTFee,
          t.PaymentMode,
          t.PaymentType
        FROM tblParkwiz_Parking_Data t
        ${companyFilter ? 'LEFT JOIN Plaza p ON t.PlazaCode = p.PlazaCode' : ''}
        WHERE t.ExitDateTime IS NOT NULL
        ${companyFilter}
        ${plazaFilter}
        ORDER BY t.ExitDateTime DESC
      `;
      
      const result = await db.query(recentTransactionsQuery, queryParams);
      
      return res.status(200).json({
        success: true,
        data: result.recordset,
        message: 'Recent transactions retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getRecentTransactions:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve recent transactions',
        error: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET PEAK HOURS DATA
   * ===============================================================================
   *
   * Returns transaction counts by hour of day to identify peak hours.
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with hourly transaction data
   */
  getPeakHoursData: async (req, res) => {
    try {
      const { dateRange = 'today', companyId, plazaId } = req.query;
      const { id: userId, role } = req.user;
      
      // Calculate date range
      const { startDate, endDate } = calculateDateRange(dateRange);
      
      // Base query parameters
      const queryParams = { 
        startDate, 
        endDate 
      };
      
      // Apply role-based filtering
      let companyFilter = '';
      let plazaFilter = '';
      
      if (role === 'CompanyAdmin') {
        companyFilter = 'AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
      } else if (role === 'PlazaManager') {
        plazaFilter = 'AND t.PlazaCode IN (SELECT p.PlazaCode FROM Plaza p JOIN UserPlaza up ON p.Id = up.PlazaId WHERE up.UserId = @userId AND up.IsActive = 1)';
        queryParams.userId = userId;
      }
      
      // Entity-specific filtering
      if (companyId) {
        companyFilter = 'AND p.CompanyId = @companyId';
        queryParams.companyId = companyId;
      }
      
      if (plazaId) {
        plazaFilter = 'AND t.PlazaCode = (SELECT PlazaCode FROM Plaza WHERE Id = @plazaId)';
        queryParams.plazaId = plazaId;
      }
      
      // Execute peak hours query
      const peakHoursQuery = `
        SELECT
          DATEPART(HOUR, t.ExitDateTime) as hour,
          COUNT(*) as count
        FROM tblParkwiz_Parking_Data t
        ${companyFilter ? 'LEFT JOIN Plaza p ON t.PlazaCode = p.PlazaCode' : ''}
        WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
        ${companyFilter}
        ${plazaFilter}
        GROUP BY DATEPART(HOUR, t.ExitDateTime)
        ORDER BY hour
      `;
      
      const result = await db.query(peakHoursQuery, queryParams);
      
      // Fill in missing hours with zero counts
      const hourlyData = Array(24).fill().map((_, i) => ({
        hour: i,
        count: 0
      }));
      
      result.recordset.forEach(row => {
        hourlyData[row.hour].count = row.count;
      });
      
      return res.status(200).json({
        success: true,
        data: hourlyData,
        message: 'Peak hours data retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getPeakHoursData:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve peak hours data',
        error: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET DAILY REVENUE DATA
   * ===============================================================================
   *
   * Returns daily revenue data for the transaction overview chart.
   * Shows revenue trends over time with day-wise breakdown.
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with daily revenue data
   */
  getDailyRevenueData: async (req, res) => {
    try {
      console.log('🚀 Daily Revenue Data Request Started');
      const { dateRange = 'week', companyId, plazaId } = req.query;
      const { id: userId, role } = req.user;
      
      console.log('👤 User Info:', { userId, role });
      console.log('🔧 Request Filters:', { dateRange, companyId, plazaId });

      // Calculate date range
      const { startDate, endDate } = calculateDateRange(dateRange);

      // Base query parameters
      const queryParams = {
        startDate,
        endDate
      };

      // Build WHERE clause based on user role and filters
      let whereClause = 'WHERE t.ExitDateTime BETWEEN @startDate AND @endDate';

      // Apply role-based filtering
      if (role === 'CompanyAdmin') {
        whereClause += ' AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
      } else if (role === 'PlazaManager') {
        whereClause += ' AND p.Id IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
      }

      // Apply additional filters
      if (companyId) {
        whereClause += ' AND p.CompanyId = @companyId';
        queryParams.companyId = companyId;
      }

      if (plazaId) {
        whereClause += ' AND p.Id = @plazaId';
        queryParams.plazaId = plazaId;
      }

      // Execute daily revenue query with logging
      const dailyRevenueQuery = `
        SELECT
          CAST(t.ExitDateTime AS DATE) as date,
          ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as revenue,
          COUNT(*) as transactions,
          AVG(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)) as avgRevenue
        FROM tblParkwiz_Parking_Data t
        LEFT JOIN Plaza p ON t.PlazaCode = p.PlazaCode
        ${whereClause}
        GROUP BY CAST(t.ExitDateTime AS DATE)
        ORDER BY date ASC
      `;

      console.log('🔍 Executing Daily Revenue Query:', dailyRevenueQuery);
      console.log('📊 Daily Revenue Query Parameters:', queryParams);
      
      const result = await db.query(dailyRevenueQuery, queryParams);
      console.log('✅ Daily Revenue Query Result Count:', result.recordset.length);

      // Format the data for the chart
      const formattedData = result.recordset.map(row => ({
        date: row.date,
        revenue: parseFloat(row.revenue) || 0,
        transactions: parseInt(row.transactions) || 0,
        avgRevenue: parseFloat(row.avgRevenue) || 0,
        // Format date for display
        label: new Date(row.date).toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric'
        })
      }));

      return res.status(200).json({
        success: true,
        data: formattedData,
        message: 'Daily revenue data retrieved successfully'
      });
    } catch (error) {
      console.error('❌ Error in getDailyRevenueData:', error);
      console.error('📋 Daily Revenue Error Details:', {
        message: error.message,
        code: error.code,
        stack: error.stack,
        queryParams: queryParams || 'Not available'
      });
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve daily revenue data',
        error: error.message,
        errorCode: error.code
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET REVENUE BY PLAZA
   * ===============================================================================
   *
   * Returns revenue breakdown by plaza with transaction counts.
   * Only accessible to SuperAdmin and CompanyAdmin roles.
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with plaza revenue data
   */
  getRevenueByPlaza: async (req, res) => {
    try {
      const { dateRange = 'today', companyId } = req.query;
      const { id: userId, role } = req.user;
      
      // Only SuperAdmin and CompanyAdmin can access this endpoint
      if (role !== 'SuperAdmin' && role !== 'CompanyAdmin') {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to access this resource'
        });
      }
      
      // Calculate date range
      const { startDate, endDate } = calculateDateRange(dateRange);
      
      // Base query parameters
      const queryParams = { 
        startDate, 
        endDate 
      };
      
      // Apply role-based filtering
      let companyFilter = '';
      
      if (role === 'CompanyAdmin') {
        companyFilter = 'AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
      }
      
      // Entity-specific filtering
      if (companyId) {
        companyFilter = 'AND p.CompanyId = @companyId';
        queryParams.companyId = companyId;
      }
      
      // Execute plaza revenue query
      const plazaRevenueQuery = `
        SELECT
          t.PlazaName,
          ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as totalRevenue,
          COUNT(*) as transactionCount
        FROM tblParkwiz_Parking_Data t
        ${companyFilter ? 'LEFT JOIN Plaza p ON t.PlazaCode = p.PlazaCode' : ''}
        WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
        ${companyFilter}
        GROUP BY t.PlazaName
        ORDER BY totalRevenue DESC
      `;
      
      const result = await db.query(plazaRevenueQuery, queryParams);
      
      return res.status(200).json({
        success: true,
        data: result.recordset,
        message: 'Revenue by plaza retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getRevenueByPlaza:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve revenue by plaza',
        error: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET LANE STATUS
   * ===============================================================================
   *
   * Returns the current status of lanes for a specific plaza.
   * Primarily used by PlazaManager role.
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with lane status data
   */
  getLaneStatus: async (req, res) => {
    try {
      const { plazaId } = req.query;
      const { id: userId, role } = req.user;
      
      if (!plazaId) {
        return res.status(400).json({
          success: false,
          message: 'Plaza ID is required'
        });
      }
      
      // Check if user has access to this plaza
      if (role !== 'SuperAdmin') {
        let accessQuery;
        
        if (role === 'CompanyAdmin') {
          accessQuery = `
            SELECT COUNT(*) as count
            FROM Plaza p
            JOIN UserCompany uc ON p.CompanyId = uc.CompanyId
            WHERE p.Id = @plazaId AND uc.UserId = @userId AND uc.IsActive = 1
          `;
        } else if (role === 'PlazaManager') {
          accessQuery = `
            SELECT COUNT(*) as count
            FROM UserPlaza
            WHERE PlazaId = @plazaId AND UserId = @userId AND IsActive = 1
          `;
        }
        
        const accessResult = await db.query(accessQuery, {
          plazaId,
          userId
        });
        
        if (accessResult.recordset[0].count === 0) {
          return res.status(403).json({
            success: false,
            message: 'You do not have access to this plaza'
          });
        }
      }
      
      // Get lane status
      const laneStatusQuery = `
        SELECT
          l.LaneID,
          l.LaneNumber,
          l.LaneType,
          l.ActiveStatus,
          CASE 
            WHEN l.ActiveStatus = 1 THEN 'Active'
            ELSE 'Inactive'
          END as Status,
          (
            SELECT COUNT(*)
            FROM tblParkwiz_Parking_Data t
            WHERE (t.EntryLane = l.LaneNumber OR t.ExitLane = l.LaneNumber)
            AND t.PlazaCode = p.PlazaCode
            AND t.ExitDateTime >= DATEADD(HOUR, -24, GETDATE())
          ) as TransactionsLast24Hours
        FROM tblLaneDetails l
        JOIN Plaza p ON l.PlazaID = p.Id
        WHERE l.PlazaID = @plazaId
        ORDER BY l.LaneNumber
      `;
      
      const result = await db.query(laneStatusQuery, { plazaId });
      
      return res.status(200).json({
        success: true,
        data: result.recordset,
        message: 'Lane status retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getLaneStatus:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve lane status',
        error: error.message
      });
    }
  }
};

module.exports = dashboardController;