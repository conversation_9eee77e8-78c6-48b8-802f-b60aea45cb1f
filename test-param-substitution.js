// Test parameter substitution logic
const queryParams = {
  startDate: new Date('2025-06-30T06:00:00.000Z'),
  endDate: new Date('2025-07-01T06:00:00.000Z'),
  companyId: 123,
  plazaId: 456
};

const testQuery = `
SELECT * FROM tblParkwiz_Parking_Data t
WHERE t.EntryDateTime BETWEEN @startDate AND @endDate
AND t.CompanyId = @companyId
AND t.PlazaId = @plazaId
`;

console.log('Original Query:');
console.log(testQuery);

console.log('\nParameters:');
Object.keys(queryParams).forEach(key => {
  const value = queryParams[key];
  if (value instanceof Date) {
    console.log(`  @${key}: '${value.toISOString().replace('T', ' ').substring(0, 19)}'`);
  } else {
    console.log(`  @${key}: ${typeof value === 'string' ? `'${value}'` : value}`);
  }
});

// Parameter substitution
let debugQuery = testQuery;
Object.keys(queryParams).forEach(key => {
  const value = queryParams[key];
  const paramPlaceholder = `@${key}`;
  let substitutedValue;
  
  if (value instanceof Date) {
    substitutedValue = `'${value.toISOString().replace('T', ' ').substring(0, 19)}'`;
  } else if (typeof value === 'string') {
    substitutedValue = `'${value}'`;
  } else {
    substitutedValue = value;
  }
  
  debugQuery = debugQuery.replace(new RegExp(`\\${paramPlaceholder}\\b`, 'g'), substitutedValue);
});

console.log('\nQuery with substituted values:');
console.log('=====================================');
console.log(debugQuery);
console.log('=====================================');